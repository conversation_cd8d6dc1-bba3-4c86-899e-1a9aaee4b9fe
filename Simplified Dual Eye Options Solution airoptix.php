
/**
 * Simplified Dual Eye Options Solution airoptix
 */
/**
 * Custom dual options selection for product ID 222486
 * Fixed cart integration
 */

// Hook to replace the add to cart form for our specific product
add_action('woocommerce_before_single_product', 'check_for_custom_product');
function check_for_custom_product() {
    global $product;

    if (!is_object($product)) {
        return;
    }

    // Check if this is our target product
    if ($product->get_id() == 222486) {
        // Remove default variation form
        remove_action('woocommerce_variable_add_to_cart', 'woocommerce_variable_add_to_cart', 30);

        // Add our custom form
        add_action('woocommerce_variable_add_to_cart', 'dual_eye_options_form', 30);
    }
}

// Add this near the top of your file, after the initial hooks
add_action('wp_enqueue_scripts', 'enqueue_custom_cart_script');

function enqueue_custom_cart_script() {
    // Only on our specific product page
    if (is_product() && get_the_ID() == 222486) {
        wp_enqueue_script('wc-add-to-cart');
        wp_enqueue_script('wc-cart-fragments');

        // Register and enqueue our custom script
        wp_register_script('dual-eye-cart', false, array('jquery', 'wc-add-to-cart'), '1.0', true);
        wp_enqueue_script('dual-eye-cart');

        // Localize the script
        wp_localize_script('dual-eye-cart', 'dual_eye_cart_params', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'wc_ajax_url' => WC_AJAX::get_endpoint('%%endpoint%%'),
            'product_id' => get_the_ID()
        ));

        // Add inline script
        wp_add_inline_script('dual-eye-cart', '
            jQuery(document).ready(function($) {
                $("form.cart").on("submit", function(e) {
                    e.preventDefault();

                    // Clear previous messages
                    $(".form-message").removeClass("error success").html("");

                    var sph_levo = $("#sph_levo").val();
                    var sph_desno = $("#sph_desno").val();
                    var boja_levo = $("#boja_levo").val();
                    var boja_desno = $("#boja_desno").val();
                    var quantity_levo = $("#quantity_levo").val();
                    var quantity_desno = $("#quantity_desno").val();

                    // Validation
                    if ((sph_levo && !boja_levo) || (boja_levo && !sph_levo)) {
                        $(".form-message").addClass("error").html("Za levo oko morate izabrati i SPH i BOJA opcije.");
                        return false;
                    }

                    if ((sph_desno && !boja_desno) || (boja_desno && !sph_desno)) {
                        $(".form-message").addClass("error").html("Za desno oko morate izabrati i SPH i BOJA opcije.");
                        return false;
                    }

                    if (!((sph_levo && boja_levo) || (sph_desno && boja_desno))) {
                        $(".form-message").addClass("error").html("Morate izabrati barem jednu kompletnu opciju (SPH i BOJA).");
                        return false;
                    }

                    // Validate quantities
                    if ((sph_levo && boja_levo) && (!quantity_levo || quantity_levo < 1)) {
                        $(".form-message").addClass("error").html("Količina za levo oko mora biti najmanje 1.");
                        return false;
                    }

                    if ((sph_desno && boja_desno) && (!quantity_desno || quantity_desno < 1)) {
                        $(".form-message").addClass("error").html("Količina za desno oko mora biti najmanje 1.");
                        return false;
                    }

                    // Get form data
                    var formData = $(this).serialize();
                    formData += "&action=dual_eye_cart_add";

                    // Add custom data
                    if (sph_levo && boja_levo) {
                        formData += "&left_eye=1&left_sph=" + sph_levo + "&left_boja=" + boja_levo + "&quantity_levo=" + quantity_levo;
                    }

                    if (sph_desno && boja_desno) {
                        formData += "&right_eye=1&right_sph=" + sph_desno + "&right_boja=" + boja_desno + "&quantity_desno=" + quantity_desno;
                    }

                    // Disable submit button
                    var $submitButton = $(this).find(".single_add_to_cart_button");
                    $submitButton.prop("disabled", true);

                    // AJAX request
                    $.ajax({
                        type: "POST",
                        url: dual_eye_cart_params.ajax_url,
                        data: formData,
                        success: function(response) {
                            if (response.fragments) {
                                $.each(response.fragments, function(key, value) {
                                    $(key).replaceWith(value);
                                });
                                $(".form-message").addClass("success").html("Uspešno dodato u korpu!");

                                // Reset form fields after successful addition
                                $("#sph_levo, #boja_levo, #sph_desno, #boja_desno").val("").trigger("change");
                                $("#quantity_levo, #quantity_desno").val(1);

                                // Update cart display
                                $(document.body).trigger("added_to_cart", [response.fragments, response.cart_hash]);
                            } else {
                                $(".form-message").addClass("error").html(response.data.message || "Došlo je do greške.");
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error("Ajax error:", status, error);
                            $(".form-message").addClass("error").html("Došlo je do greške. Pokušajte ponovo.");
                        },
                        complete: function() {
                            $submitButton.prop("disabled", false);
                        }
                    });

                    return false;
                });
            });
        ');
    }
}

// Display our custom form
function dual_eye_options_form() {
    global $product;

    // Double-check product ID
    if ($product->get_id() != 222486) {
        return;
    }

    // Get product attributes
    $attributes = $product->get_variation_attributes();

    // Check if required attributes exist
    if (!isset($attributes['pa_lsph']) || !isset($attributes['pa_boja-sociva'])) {
        echo 'Required attributes not found.';
        return;
    }

    $sph_options = $attributes['pa_lsph'];
    $boja_options = $attributes['pa_boja-sociva'];

    // Get attribute taxonomies to display proper names
    $sph_taxonomy = get_taxonomy('pa_lsph');
    $boja_taxonomy = get_taxonomy('pa_boja-sociva');

    // Get attribute terms to display proper names
    $sph_terms = array();
    $boja_terms = array();

    foreach ($sph_options as $slug) {
        $term = get_term_by('slug', $slug, 'pa_lsph');
        if ($term) {
            $sph_terms[$slug] = $term->name;
        } else {
            $sph_terms[$slug] = $slug;
        }
    }

    foreach ($boja_options as $slug) {
        $term = get_term_by('slug', $slug, 'pa_boja-sociva');
        if ($term) {
            $boja_terms[$slug] = $term->name;
        } else {
            $boja_terms[$slug] = $slug;
        }
    }

    // Get available variations
    $available_variations = $product->get_available_variations();
    $variations_json = wp_json_encode($available_variations);

    // Get cart URL
    $cart_url = wc_get_cart_url();

    ?>
    <form class="cart" method="post" enctype="multipart/form-data">
        <div class="woocommerce dual-eye-options-container">
            <div class="dual-options-form-wrapper">
                <div class="eye-options">
                    <h3 style="font-family: 'Outfit', sans-serif;">Levo oko</h3>
                    <div class="option-row">
                        <label for="sph_levo">SPH</label>
                        <select id="sph_levo" name="sph_levo">
                            <option value="">Odaberite opciju</option>
                            <?php foreach ($sph_options as $sph) : ?>
                                <option value="<?php echo esc_attr($sph); ?>"><?php echo esc_html($sph_terms[$sph]); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="option-row">
                        <label for="boja_levo">BOJA</label>
                        <select id="boja_levo" name="boja_levo">
                            <option value="">Odaberite opciju</option>
                            <?php foreach ($boja_options as $boja) : ?>
                                <option value="<?php echo esc_attr($boja); ?>"><?php echo esc_html($boja_terms[$boja]); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="option-row quantity-row">
                        <label for="quantity_levo">KOLIČINA</label>
                        <div class="quantity-controls">
                            <button type="button" class="quantity-btn minus" data-target="quantity_levo">-</button>
                            <div class="qty-input-wrapper">
                                <input type="number" id="quantity_levo" class="input-text qty text" step="1" min="1" max="" name="quantity_levo" value="1" title="Qty" size="4" inputmode="numeric">
                            </div>
                            <button type="button" class="quantity-btn plus" data-target="quantity_levo">+</button>
                        </div>
                    </div>
                </div>

                <div class="vertical-divider"></div>

                <div class="eye-options">
                    <h3 style="font-family: 'Outfit', sans-serif;">Desno oko</h3>
                    <div class="option-row">
                        <label for="sph_desno">SPH</label>
                        <select id="sph_desno" name="sph_desno">
                            <option value="">Odaberite opciju</option>
                            <?php foreach ($sph_options as $sph) : ?>
                                <option value="<?php echo esc_attr($sph); ?>"><?php echo esc_html($sph_terms[$sph]); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="option-row">
                        <label for="boja_desno">BOJA</label>
                        <select id="boja_desno" name="boja_desno">
                            <option value="">Odaberite opciju</option>
                            <?php foreach ($boja_options as $boja) : ?>
                                <option value="<?php echo esc_attr($boja); ?>"><?php echo esc_html($boja_terms[$boja]); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="option-row quantity-row">
                        <label for="quantity_desno">KOLIČINA</label>
                        <div class="quantity-controls">
                            <button type="button" class="quantity-btn minus" data-target="quantity_desno">-</button>
                            <div class="qty-input-wrapper">
                                <input type="number" id="quantity_desno" class="input-text qty text" step="1" min="1" max="" name="quantity_desno" value="1" title="Qty" size="4" inputmode="numeric">
                            </div>
                            <button type="button" class="quantity-btn plus" data-target="quantity_desno">+</button>
                        </div>
                    </div>
                </div>

                <div class="add-to-cart-row">
                    <input type="hidden" name="product_id" value="<?php echo esc_attr($product->get_id()); ?>">
                    <input type="hidden" name="dual_eye_form" value="1">
                    <div class="button-group">
                        <button type="button" id="reset-selections" class="reset-button">Očisti</button>
                        <button type="submit" name="add-to-cart" value="<?php echo esc_attr($product->get_id()); ?>" class="single_add_to_cart_button button alt">DODAJ U KORPU</button>
                    </div>
                </div>
                <div class="form-message"></div>
            </div>
        </div>
    </form>

    <style>
        /* Custom styling */
        .dual-eye-options-container {
            margin-bottom: 30px;
        }
        .dual-options-form-wrapper {
            display: flex;
            flex-wrap: wrap;
			justify-content:space-between;
            position: relative;
        }
        .eye-options {
            width: 45%;
            margin-bottom: 20px;
            position: relative;
        }
        .vertical-divider {
            width: 1px;
            background-color: #ddd;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            top: 0;
            bottom:150;
        }

        .eye-options h3 {
            color:#F38120;
            text-transform: uppercase;
        }
        .option-row {
            margin-bottom: 15px;
        }
        .option-row label {
            display: block;
            font-family: 'Outfit', sans-serif;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .option-row select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Outfit', sans-serif;
            font-size: 16px;
        }
        .add-to-cart-row {
            width: 100%;
            display: flex;
            align-items: center;
            margin-top: 15px;
        }
        .qty-input-wrapper {
            display: inline-block;
        }
        .quantity-row input {
            width: 40px;
            padding: 8px;
            border: 1px solid #ddd;
			 border-radius: 5px;

            font-family: 'Outfit', sans-serif;
            font-size: 16px;
            text-align: center;
            margin: 0 20px;
            height: 40px;
            box-sizing: border-box;
            /* Remove spinner arrows for all browsers */
            -moz-appearance: textfield; /* Firefox */
        }
        /* Remove spinner arrows for Chrome, Safari, Edge, Opera */
        .quantity-row input::-webkit-outer-spin-button,
        .quantity-row input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
        .quantity-controls {
            display: flex;
            align-items: center;
            justify-content: flex-start;
        }
        .quantity-btn {
            width: 40px;
            height: 40px;
            background-color: white!important;
            color: #333;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 18px;
            line-height: 1;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
           margin:0 !important;

            box-sizing: border-box;
        }
        .quantity-btn:hover {
          			color:white;
        }
.elementor-379 .elementor-element.elementor-element-4b0dc44 button:hover {
  background-color: #F38120!important;
}

        
        
        .single_add_to_cart_button {
            background-color: #f5c6a1 !important;
            color: white !important;
            font-family: 'Outfit', sans-serif;
            font-size: 16px;
            padding: 12px 25px;
            margin: 0 !important;
            text-transform: uppercase;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .single_add_to_cart_button:hover {
            background-color: #f0b78e !important;
        }
        .button-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 100%;
        }
        .reset-button {
            background-color: white !important;
            background-image: none !important;
            color: #333 !important;
            font-family: 'Outfit', sans-serif !important;
            font-size: 14px !important;
            padding: 0 !important;
            margin: 0 0 5px 0 !important;
            border: none !important;
            box-shadow: none !important;
            cursor: pointer;
            text-align: center;
            text-decoration: none !important;
            width: 100%;
            min-height: auto !important;
            line-height: normal !important;
            transition: none !important;
        }
        .form-message {
            margin-top: 10px;
            width: 100%;
            padding: 10px;
            font-family: 'Outfit', sans-serif;
        }
        .form-message.error {
            color: red;
            background-color: #fff0f0;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
        }
        button#reset-selections {
            background-color: transparent !important;
        }

        /* Mobile responsive styles */
        @media (max-width: 768px) {
            .eye-options {
                width: 100%;
                margin-right: 0;
            }
            .eye-options:first-child:after {
                display: none;
            }
            .vertical-divider {
                display: none;
            }
            .option-row label,
            .option-row select {
                font-size: 14px;
            }
            .single_add_to_cart_button {
                width: 100%;
                padding: 10px 15px;
            }
            .button-group {
                width: 100%;
            }
            .quantity-controls {
                justify-content: center;
            }
            .quantity-btn {
                width: 25px;
                height: 30px;
                font-size: 16px;
                margin: 0 5px;
                box-sizing: border-box;
            }
            .quantity-row input {
                height: 30px;
                box-sizing: border-box;
            }
        }
    </style>

    <script type="text/javascript">
    jQuery(document).ready(function($) {
        // Store variations data
        var variations = <?php echo $variations_json; ?>;

        // Form submission is handled by the script registered in enqueue_custom_cart_script

        // Reset button functionality
        $('#reset-selections').on('click', function() {
            // Reset all select elements to their first option
            $('#sph_levo, #boja_levo, #sph_desno, #boja_desno').each(function() {
                $(this).val('').trigger('change');
            });

            // Reset quantities to 1
            $('#quantity_levo, #quantity_desno').val(1);

            // Clear any form messages
            $('.form-message').empty();

            // Show a message that selections have been reset
            $('.form-message').html('<div style="color: green; padding: 10px; background-color: #f0fff0; border: 1px solid #ddd; border-radius: 4px;">Odabiri i količine su poništeni.</div>');

            // Hide the message after 3 seconds
            setTimeout(function() {
                $('.form-message').empty();
            }, 3000);
        });

        // Quantity plus button functionality
        $('.quantity-btn.plus').on('click', function() {
            var targetInput = $(this).data('target');
            var $input = $('#' + targetInput);
            var currentVal = parseInt($input.val());

            if (!isNaN(currentVal)) {
                $input.val(currentVal + 1);
            } else {
                $input.val(1);
            }
        });

        // Quantity minus button functionality
        $('.quantity-btn.minus').on('click', function() {
            var targetInput = $(this).data('target');
            var $input = $('#' + targetInput);
            var currentVal = parseInt($input.val());

            if (!isNaN(currentVal) && currentVal > 1) {
                $input.val(currentVal - 1);
            } else {
                $input.val(1);
            }
        });
    });
    </script>
    <?php
}

// Add our AJAX handler
add_action('wp_ajax_dual_eye_cart_add', 'handle_dual_eye_ajax_add_to_cart');
add_action('wp_ajax_nopriv_dual_eye_cart_add', 'handle_dual_eye_ajax_add_to_cart');

function handle_dual_eye_ajax_add_to_cart() {
    // Debug log
    error_log('AJAX handler called: dual_eye_ajax_add_to_cart');
    error_log('POST data: ' . print_r($_POST, true));

    $product_id = isset($_POST['product_id']) ? absint($_POST['product_id']) : 0;

    if ($product_id != 222486) {
        wp_send_json_error(array('message' => 'Invalid product'));
        return;
    }

    // Get quantities for each eye
    $quantity_levo = isset($_POST['quantity_levo']) ? wc_stock_amount($_POST['quantity_levo']) : 1;
    $quantity_desno = isset($_POST['quantity_desno']) ? wc_stock_amount($_POST['quantity_desno']) : 1;

    // Get form selections
    $sph_levo = isset($_POST['sph_levo']) ? sanitize_text_field($_POST['sph_levo']) : '';
    $boja_levo = isset($_POST['boja_levo']) ? sanitize_text_field($_POST['boja_levo']) : '';
    $sph_desno = isset($_POST['sph_desno']) ? sanitize_text_field($_POST['sph_desno']) : '';
    $boja_desno = isset($_POST['boja_desno']) ? sanitize_text_field($_POST['boja_desno']) : '';

    // Check if we have right eye data
    $has_right_eye = isset($_POST['right_eye']) && $_POST['right_eye'] == 1;
    // If we don't have explicit right eye flag but have right eye data, set it
    if (!$has_right_eye && $sph_desno && $boja_desno) {
        $_POST['right_eye'] = 1;
        error_log('Setting right_eye=1 because we have right eye data');
    }

    // Check if we have left eye data
    $has_left_eye = isset($_POST['left_eye']) && $_POST['left_eye'] == 1;
    // If we don't have explicit left eye flag but have left eye data, set it
    if (!$has_left_eye && $sph_levo && $boja_levo) {
        $_POST['left_eye'] = 1;
        error_log('Setting left_eye=1 because we have left eye data');
    }

    $added_to_cart = false;

    // Log current cart contents before adding
    error_log('Current cart contents before adding:');
    foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
        error_log('Item: ' . $cart_item_key . ' - Product: ' . $cart_item['product_id'] . ' - Variation: ' . $cart_item['variation_id'] . ' - Quantity: ' . $cart_item['quantity']);
    }

    // Process left eye
    if ($sph_levo && $boja_levo) {
        error_log('Processing left eye: SPH=' . $sph_levo . ', BOJA=' . $boja_levo . ', QTY=' . $quantity_levo);

        $variation_id = find_variation_id($product_id, array(
            'attribute_pa_lsph' => $sph_levo,
            'attribute_pa_boja-sociva' => $boja_levo
        ));

        error_log('Left eye variation ID: ' . $variation_id);

        if ($variation_id) {
            // Check if this variation with the same eye info is already in cart
            $cart_item_key = find_cart_item_key($product_id, $variation_id, 'levo');

            if ($cart_item_key) {
                // If already in cart, update quantity
                error_log('Left eye item found in cart with key: ' . $cart_item_key);
                $current_qty = WC()->cart->get_cart_item($cart_item_key)['quantity'];
                $new_qty = $current_qty + $quantity_levo;
                error_log('Updating left eye quantity from ' . $current_qty . ' to ' . $new_qty);
                $result = WC()->cart->set_quantity($cart_item_key, $new_qty);
                if ($result) {
                    $added_to_cart = true;
                    error_log('Left eye quantity updated successfully');
                } else {
                    error_log('Failed to update left eye quantity');
                }
            } else {
                // If not in cart, add new item
                error_log('Left eye item not found in cart, adding new item with quantity: ' . $quantity_levo);

                // Set left eye info in cart item data
                $cart_item_data = array(
                    'eye_info' => 'levo',
                    'unique_key' => 'left_eye_' . $product_id . '_' . $variation_id,
                    'wcff_unique_key' => md5('left_eye_' . $product_id . '_' . $variation_id)
                );

                error_log('Cart item data: ' . print_r($cart_item_data, true));

                // Try to directly set the cart item data
                WC()->cart->cart_contents_count; // Ensure cart is loaded

                $result = WC()->cart->add_to_cart(
                    $product_id,
                    $quantity_levo, // Use left eye quantity
                    $variation_id,
                    array(
                        'attribute_pa_lsph' => $sph_levo,
                        'attribute_pa_boja-sociva' => $boja_levo
                    ),
                    $cart_item_data
                );

                if ($result) {
                    $added_to_cart = true;
                    error_log('Left eye added to cart successfully with key: ' . $result);
                } else {
                    error_log('Failed to add left eye to cart');
                }
            }
        } else {
            error_log('Could not find variation ID for left eye');
        }
    }

    // Process right eye
    if ($sph_desno && $boja_desno) {
        error_log('Processing right eye: SPH=' . $sph_desno . ', BOJA=' . $boja_desno . ', QTY=' . $quantity_desno);

        $variation_id = find_variation_id($product_id, array(
            'attribute_pa_lsph' => $sph_desno,
            'attribute_pa_boja-sociva' => $boja_desno
        ));

        error_log('Right eye variation ID: ' . $variation_id);

        if ($variation_id) {
            // Check if this variation with the same eye info is already in cart
            $cart_item_key = find_cart_item_key($product_id, $variation_id, 'desno');

            if ($cart_item_key) {
                // If already in cart, update quantity
                error_log('Right eye item found in cart with key: ' . $cart_item_key);
                $current_qty = WC()->cart->get_cart_item($cart_item_key)['quantity'];
                $new_qty = $current_qty + $quantity_desno;
                error_log('Updating right eye quantity from ' . $current_qty . ' to ' . $new_qty);
                $result = WC()->cart->set_quantity($cart_item_key, $new_qty);
                if ($result) {
                    $added_to_cart = true;
                    error_log('Right eye quantity updated successfully');
                } else {
                    error_log('Failed to update right eye quantity');
                }
            } else {
                // If not in cart, add new item
                error_log('Right eye item not found in cart, adding new item with quantity: ' . $quantity_desno);

                // Set right eye info in cart item data
                $cart_item_data = array(
                    'eye_info' => 'desno',
                    'unique_key' => 'right_eye_' . $product_id . '_' . $variation_id,
                    'wcff_unique_key' => md5('right_eye_' . $product_id . '_' . $variation_id)
                );

                error_log('Cart item data: ' . print_r($cart_item_data, true));

                // Try to directly set the cart item data
                WC()->cart->cart_contents_count; // Ensure cart is loaded

                $result = WC()->cart->add_to_cart(
                    $product_id,
                    $quantity_desno, // Use right eye quantity
                    $variation_id,
                    array(
                        'attribute_pa_lsph' => $sph_desno,
                        'attribute_pa_boja-sociva' => $boja_desno
                    ),
                    $cart_item_data
                );

                if ($result) {
                    $added_to_cart = true;
                    error_log('Right eye added to cart successfully with key: ' . $result);
                } else {
                    error_log('Failed to add right eye to cart');
                }
            }
        } else {
            error_log('Could not find variation ID for right eye');
        }
    }

    // Log cart contents after adding
    error_log('Cart contents after adding:');
    foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
        error_log('Item: ' . $cart_item_key . ' - Product: ' . $cart_item['product_id'] . ' - Variation: ' . $cart_item['variation_id'] . ' - Quantity: ' . $cart_item['quantity'] . ' - Eye Info: ' . (isset($cart_item['eye_info']) ? $cart_item['eye_info'] : 'not set'));
    }

    if ($added_to_cart) {
        WC_AJAX::get_refreshed_fragments();
    } else {
        wp_send_json_error(array('message' => 'Failed to add to cart'));
    }

    wp_die();
}

// Helper function to find variation ID
function find_variation_id($product_id, $attributes) {
    $product = wc_get_product($product_id);
    if (!$product || $product->get_type() !== 'variable') {
        return 0;
    }

    $data_store = WC_Data_Store::load('product');
    return $data_store->find_matching_product_variation($product, $attributes);
}

// Helper function to find cart item key by eye info
function find_cart_item_key($product_id, $variation_id, $eye_info) {
    // Debug log
    error_log('Looking for cart item: Product ID=' . $product_id . ', Variation ID=' . $variation_id . ', Eye Info=' . $eye_info);

    // Generate the expected cart ID
    $data_store = WC_Data_Store::load('product');
    $product = wc_get_product($product_id);

    // Base cart ID that WooCommerce would generate
    $base_cart_id = $data_store->get_cart_id($product_id, $variation_id, array(), array(
        'attribute_pa_lsph' => $product->get_variation_attributes()['pa_lsph'],
        'attribute_pa_boja-sociva' => $product->get_variation_attributes()['pa_boja-sociva']
    ));

    // Expected cart ID with eye info
    $expected_cart_id = $base_cart_id . '_' . $eye_info;
    error_log('Expected cart ID: ' . $expected_cart_id);

    // First try to find by exact cart ID
    if (isset(WC()->cart->cart_contents[$expected_cart_id])) {
        error_log('Found exact matching cart item with key: ' . $expected_cart_id);
        return $expected_cart_id;
    }

    // If not found by exact ID, search through all cart items
    foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
        error_log('Cart item: Key=' . $cart_item_key . ', Product ID=' . $cart_item['product_id'] . ', Variation ID=' . $cart_item['variation_id'] . ', Eye Info=' . (isset($cart_item['eye_info']) ? $cart_item['eye_info'] : 'not set'));

        if ($cart_item['product_id'] == $product_id &&
            $cart_item['variation_id'] == $variation_id &&
            isset($cart_item['eye_info']) &&
            $cart_item['eye_info'] == $eye_info) {
            error_log('Found matching cart item with key: ' . $cart_item_key);
            return $cart_item_key;
        }
    }
    error_log('No matching cart item found');
    return false;
}

// Display eye info in cart
add_filter('woocommerce_get_item_data', 'display_eye_info_in_cart', 10, 2);
function display_eye_info_in_cart($item_data, $cart_item) {
    error_log('Display eye info for cart item: ' . print_r($cart_item, true));

    // Only process our specific product
    if ($cart_item['product_id'] != 222486) {
        return $item_data;
    }

    // Remove any existing SPH and BOJA entries that might be added by WooCommerce
    foreach ($item_data as $key => $data) {
        if ($data['key'] == 'SPH' || $data['key'] == 'BOJA' ||
            $data['key'] == 'pa_lsph' || $data['key'] == 'pa_boja-sociva') {
            unset($item_data[$key]);
        }
    }

    // Reindex the array after removing items
    $item_data = array_values($item_data);

    // Get product variation
    $product_variation = wc_get_product($cart_item['variation_id']);

    if ($product_variation) {
        // Get variation attributes
        $attributes = $product_variation->get_attributes();

        // Get SPH and BOJA values
        $sph_slug = isset($attributes['pa_lsph']) ? $attributes['pa_lsph'] : '';
        $boja_slug = isset($attributes['pa_boja-sociva']) ? $attributes['pa_boja-sociva'] : '';

        // Get proper attribute names
        $sph_term = get_term_by('slug', $sph_slug, 'pa_lsph');
        $boja_term = get_term_by('slug', $boja_slug, 'pa_boja-sociva');

        $sph_name = $sph_term ? $sph_term->name : $sph_slug;
        $boja_name = $boja_term ? $boja_term->name : $boja_slug;

        // Add SPH and BOJA to cart item data
        if ($sph_name) {
            $item_data[] = array(
                'key'   => 'SPH',
                'value' => $sph_name
            );
        }

        if ($boja_name) {
            $item_data[] = array(
                'key'   => 'BOJA',
                'value' => $boja_name
            );
        }
    }

    if (isset($cart_item['eye_info'])) {
        $eye = $cart_item['eye_info'];
        $quantity = $cart_item['quantity'];
        error_log('Eye info found: ' . $eye . ', Quantity: ' . $quantity);

        $item_data[] = array(
            'key'   => 'Oko',
            'value' => $eye == 'levo' ? 'Levo oko x ' . $quantity : 'Desno oko x ' . $quantity
        );
    } else {
        error_log('No eye info found in cart item');
    }

    return $item_data;
}

// Save eye info to order item
add_action('woocommerce_checkout_create_order_line_item', 'add_eye_info_to_order_items', 10, 4);
function add_eye_info_to_order_items($item, $cart_item_key, $values, $order) {
    error_log('Adding eye info to order item: ' . print_r($values, true));

    // Only process our specific product
    if ($values['product_id'] != 222486) {
        return;
    }

    // Remove any existing attribute meta that might be added by WooCommerce
    $meta_data = $item->get_meta_data();
    foreach ($meta_data as $meta) {
        if ($meta->key == 'pa_lsph' || $meta->key == 'pa_boja-sociva') {
            $item->delete_meta_data($meta->key);
        }
    }

    // Get product variation
    $product_variation = wc_get_product($values['variation_id']);

    if ($product_variation) {
        // Get variation attributes
        $attributes = $product_variation->get_attributes();

        // Get SPH and BOJA values
        $sph_slug = isset($attributes['pa_lsph']) ? $attributes['pa_lsph'] : '';
        $boja_slug = isset($attributes['pa_boja-sociva']) ? $attributes['pa_boja-sociva'] : '';

        // Get proper attribute names
        $sph_term = get_term_by('slug', $sph_slug, 'pa_lsph');
        $boja_term = get_term_by('slug', $boja_slug, 'pa_boja-sociva');

        $sph_name = $sph_term ? $sph_term->name : $sph_slug;
        $boja_name = $boja_term ? $boja_term->name : $boja_slug;

        // Add SPH and BOJA to order item meta
        if ($sph_name) {
            $item->add_meta_data('SPH', $sph_name);
        }

        if ($boja_name) {
            $item->add_meta_data('BOJA', $boja_name);
        }
    }

    if (isset($values['eye_info'])) {
        $eye = $values['eye_info'];
        $quantity = $values['quantity'];
        error_log('Eye info found in order: ' . $eye . ', Quantity: ' . $quantity);
        $item->add_meta_data('Oko', $eye == 'levo' ? 'Levo oko x ' . $quantity : 'Desno oko x ' . $quantity);
    } else {
        error_log('No eye info found in order item');
    }
}

// Filter to ensure eye_info is stored in cart item data
add_filter('woocommerce_add_cart_item_data', 'add_eye_info_to_cart_item_data', 10, 3);
function add_eye_info_to_cart_item_data($cart_item_data, $product_id, $variation_id) {
    error_log('Adding cart item data filter called');
    error_log('POST data in add_eye_info_to_cart_item_data: ' . print_r($_POST, true));

    // Get form selections
    $sph_levo = isset($_POST['sph_levo']) ? sanitize_text_field($_POST['sph_levo']) : '';
    $boja_levo = isset($_POST['boja_levo']) ? sanitize_text_field($_POST['boja_levo']) : '';
    $sph_desno = isset($_POST['sph_desno']) ? sanitize_text_field($_POST['sph_desno']) : '';
    $boja_desno = isset($_POST['boja_desno']) ? sanitize_text_field($_POST['boja_desno']) : '';

    // Check if this is for the right eye based on the form data
    $is_right_eye = false;
    $is_left_eye = false;

    // Check if the current variation matches the left eye attributes
    if ($sph_levo && $boja_levo) {
        $left_variation_id = find_variation_id($product_id, array(
            'attribute_pa_lsph' => $sph_levo,
            'attribute_pa_boja-sociva' => $boja_levo
        ));

        if ($left_variation_id == $variation_id) {
            $is_left_eye = true;
            error_log('Identified as left eye based on attributes');
        }
    }

    // Check if the current variation matches the right eye attributes
    if ($sph_desno && $boja_desno) {
        $right_variation_id = find_variation_id($product_id, array(
            'attribute_pa_lsph' => $sph_desno,
            'attribute_pa_boja-sociva' => $boja_desno
        ));

        if ($right_variation_id == $variation_id) {
            $is_right_eye = true;
            error_log('Identified as right eye based on attributes');
        }
    }

    // Check explicit flags
    if (isset($_POST['left_eye']) && $_POST['left_eye'] == 1) {
        $is_left_eye = true;
        error_log('Identified as left eye based on left_eye flag');
    }

    if (isset($_POST['right_eye']) && $_POST['right_eye'] == 1) {
        $is_right_eye = true;
        error_log('Identified as right eye based on right_eye flag');
    }

    // If both flags are set, use the variation ID to determine which eye
    if ($is_left_eye && $is_right_eye) {
        if ($variation_id == $left_variation_id) {
            $is_right_eye = false;
            error_log('Both eyes detected, but variation matches left eye');
        } else if ($variation_id == $right_variation_id) {
            $is_left_eye = false;
            error_log('Both eyes detected, but variation matches right eye');
        }
    }

    if ($is_right_eye && !$is_left_eye) {
        error_log('Setting right eye info in cart item data');
        $cart_item_data['eye_info'] = 'desno';
        // Add a unique key for right eye
        $cart_item_data['unique_key'] = 'right_eye_' . $product_id . '_' . $variation_id;
        $cart_item_data['wcff_unique_key'] = md5('right_eye_' . $product_id . '_' . $variation_id);
    } else {
        error_log('Setting left eye info in cart item data');
        $cart_item_data['eye_info'] = 'levo';
        // Add a unique key for left eye
        $cart_item_data['unique_key'] = 'left_eye_' . $product_id . '_' . $variation_id;
        $cart_item_data['wcff_unique_key'] = md5('left_eye_' . $product_id . '_' . $variation_id);
    }

    error_log('Cart item data after filter: ' . print_r($cart_item_data, true));
    return $cart_item_data;
}

// Hide default variation attributes in cart
add_filter('woocommerce_cart_item_visible', 'hide_variation_attributes_in_cart', 10, 3);
function hide_variation_attributes_in_cart($visible, $cart_item, $cart_item_key) {
    return $visible;
}

// Hide variation attributes in product name
add_filter('woocommerce_is_attribute_in_product_name', 'hide_attributes_in_product_name', 10, 3);
function hide_attributes_in_product_name($is_in_name, $attribute, $name) {
    global $woocommerce;

    // Check if we're in the cart or checkout
    if (is_cart() || is_checkout()) {
        // Get the current cart
        $cart = WC()->cart->get_cart();

        // Check if any cart item is our specific product
        foreach ($cart as $cart_item) {
            if ($cart_item['product_id'] == 222486) {
                // Hide attributes in product name
                return false;
            }
        }
    }

    return $is_in_name;
}

// Filter to generate a unique cart item key based on eye info
add_filter('woocommerce_cart_id', 'unique_cart_id_for_eye_info', 10, 4);
function unique_cart_id_for_eye_info($cart_id, $product_id, $variation_id, $cart_item_data) {
    error_log('Cart ID filter called for ID: ' . $cart_id);

    if (isset($cart_item_data['eye_info'])) {
        $eye_info = $cart_item_data['eye_info'];
        $new_id = $cart_id . '_' . $eye_info;
        error_log('Modified cart ID from ' . $cart_id . ' to ' . $new_id);
        return $new_id;
    }

    return $cart_id;
}

// Remove the init hook
remove_action('init', 'process_dual_eye_form');

remove_action('wp_footer', 'prevent_direct_form_submission');