<?php
/**
 * Plugin Name: Dual Eye Options for AirOptix
 * Plugin URI:
 * Description: Custom dual eye options selection for AirOptix contact lenses
 * Version: 1.0.1
 * Author:
 * Author URI:
 * Text Domain: dual-eye-options
 * Domain Path: /languages
 * Requires at least: 5.0
 * Requires PHP: 7.2
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Main plugin class
 */
class Dual_Eye_Options_AirOptix {

    /**
     * Product ID for AirOptix
     */
    private $product_id = 222486;

    /**
     * Constructor
     */
    public function __construct() {
        // Check for custom product
        add_action('woocommerce_before_single_product', array($this, 'check_for_custom_product'));

        // Enqueue scripts
        add_action('wp_enqueue_scripts', array($this, 'enqueue_custom_cart_script'));

        // AJAX handlers
        add_action('wp_ajax_dual_eye_cart_add', array($this, 'handle_dual_eye_ajax_add_to_cart'));
        add_action('wp_ajax_nopriv_dual_eye_cart_add', array($this, 'handle_dual_eye_ajax_add_to_cart'));

        // Cart display
        add_filter('woocommerce_get_item_data', array($this, 'display_eye_info_in_cart'), 10, 2);
        add_action('woocommerce_checkout_create_order_line_item', array($this, 'add_eye_info_to_order_items'), 10, 4);
        add_filter('woocommerce_add_cart_item_data', array($this, 'add_eye_info_to_cart_item_data'), 10, 3);
        add_filter('woocommerce_cart_item_visible', array($this, 'hide_variation_attributes_in_cart'), 10, 3);
        add_filter('woocommerce_is_attribute_in_product_name', array($this, 'hide_attributes_in_product_name'), 10, 3);
        add_filter('woocommerce_cart_id', array($this, 'unique_cart_id_for_eye_info'), 10, 4);
    }

    /**
     * Check if current product is our target product
     */
    public function check_for_custom_product() {
        global $product;

        if (!is_object($product)) {
            return;
        }

        // Check if this is our target product
        if ($product->get_id() == $this->product_id) {
            // Remove default variation form
            remove_action('woocommerce_variable_add_to_cart', 'woocommerce_variable_add_to_cart', 30);

            // Add our custom form
            add_action('woocommerce_variable_add_to_cart', array($this, 'dual_eye_options_form'), 30);
        }
    }

    /**
     * Enqueue custom scripts
     */
    public function enqueue_custom_cart_script() {
        // Only on our specific product page
        if (is_product() && get_the_ID() == $this->product_id) {
            wp_enqueue_script('wc-add-to-cart');
            wp_enqueue_script('wc-cart-fragments');

            // Register and enqueue our custom script
            wp_register_script('dual-eye-cart', false, array('jquery', 'wc-add-to-cart'), '1.0', true);
            wp_enqueue_script('dual-eye-cart');

            // Localize the script
            wp_localize_script('dual-eye-cart', 'dual_eye_cart_params', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'wc_ajax_url' => WC_AJAX::get_endpoint('%%endpoint%%'),
                'product_id' => get_the_ID()
            ));

            // Add inline script
            wp_add_inline_script('dual-eye-cart', $this->get_inline_script());
        }
    }

    /**
     * Get inline JavaScript
     */
    private function get_inline_script() {
        return '
            jQuery(document).ready(function($) {
                $("form.cart").on("submit", function(e) {
                    e.preventDefault();

                    // Clear previous messages
                    $(".form-message").removeClass("error success").html("");

                    var sph_levo = $("#sph_levo").val();
                    var sph_desno = $("#sph_desno").val();
                    var boja_levo = $("#boja_levo").val();
                    var boja_desno = $("#boja_desno").val();
                    var quantity_levo = $("#quantity_levo").val();
                    var quantity_desno = $("#quantity_desno").val();

                    // Validation
                    if ((sph_levo && !boja_levo) || (boja_levo && !sph_levo)) {
                        $(".form-message").addClass("error").html("Za levo oko morate izabrati i SPH i BOJA opcije.");
                        return false;
                    }

                    if ((sph_desno && !boja_desno) || (boja_desno && !sph_desno)) {
                        $(".form-message").addClass("error").html("Za desno oko morate izabrati i SPH i BOJA opcije.");
                        return false;
                    }

                    if (!((sph_levo && boja_levo) || (sph_desno && boja_desno))) {
                        $(".form-message").addClass("error").html("Morate izabrati barem jednu kompletnu opciju (SPH i BOJA).");
                        return false;
                    }

                    // Validate quantities
                    if ((sph_levo && boja_levo) && (!quantity_levo || quantity_levo < 1)) {
                        $(".form-message").addClass("error").html("Količina za levo oko mora biti najmanje 1.");
                        return false;
                    }

                    if ((sph_desno && boja_desno) && (!quantity_desno || quantity_desno < 1)) {
                        $(".form-message").addClass("error").html("Količina za desno oko mora biti najmanje 1.");
                        return false;
                    }

                    // Get form data
                    var formData = $(this).serialize();
                    formData += "&action=dual_eye_cart_add";

                    // Add custom data
                    if (sph_levo && boja_levo) {
                        formData += "&left_eye=1&left_sph=" + sph_levo + "&left_boja=" + boja_levo + "&quantity_levo=" + quantity_levo;
                    }

                    if (sph_desno && boja_desno) {
                        formData += "&right_eye=1&right_sph=" + sph_desno + "&right_boja=" + boja_desno + "&quantity_desno=" + quantity_desno;
                    }

                    // Disable submit button
                    var $submitButton = $(this).find(".single_add_to_cart_button");
                    $submitButton.prop("disabled", true);

                    // AJAX request
                    $.ajax({
                        type: "POST",
                        url: dual_eye_cart_params.ajax_url,
                        data: formData,
                        success: function(response) {
                            if (response.fragments) {
                                $.each(response.fragments, function(key, value) {
                                    $(key).replaceWith(value);
                                });
                                $(".form-message").addClass("success").html("Uspešno dodato u korpu!");

                                // Reset form fields after successful addition
                                $("#sph_levo, #boja_levo, #sph_desno, #boja_desno").val("").trigger("change");
                                $("#quantity_levo, #quantity_desno").val(1);

                                // Note: The submit button will be re-enabled in the complete callback

                                // Clear the success message after 3 seconds
                                setTimeout(function() {
                                    $(".form-message").removeClass("success").empty();
                                }, 3000);

                                // Update cart display
                                $(document.body).trigger("added_to_cart", [response.fragments, response.cart_hash]);
                            } else {
                                $(".form-message").addClass("error").html(response.data.message || "Došlo je do greške.");
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error("Ajax error:", status, error);
                            $(".form-message").addClass("error").html("Došlo je do greške. Pokušajte ponovo.");
                        },
                        complete: function() {
                            $submitButton.prop("disabled", false);
                        }
                    });

                    return false;
                });
            });
        ';
    }

    /**
     * Display custom form for dual eye options
     */
    public function dual_eye_options_form() {
        global $product;

        // Double-check product ID
        if ($product->get_id() != $this->product_id) {
            return;
        }

        // Get product attributes
        $attributes = $product->get_variation_attributes();

        // Check if required attributes exist
        if (!isset($attributes['pa_lsph']) || !isset($attributes['pa_boja-sociva'])) {
            echo 'Required attributes not found.';
            return;
        }

        $sph_options = $attributes['pa_lsph'];
        $boja_options = $attributes['pa_boja-sociva'];

        // Get attribute taxonomies to display proper names
        $sph_taxonomy = get_taxonomy('pa_lsph');
        $boja_taxonomy = get_taxonomy('pa_boja-sociva');

        // Get attribute terms to display proper names
        $sph_terms = array();
        $boja_terms = array();

        foreach ($sph_options as $slug) {
            $term = get_term_by('slug', $slug, 'pa_lsph');
            if ($term) {
                $sph_terms[$slug] = $term->name;
            } else {
                $sph_terms[$slug] = $slug;
            }
        }

        foreach ($boja_options as $slug) {
            $term = get_term_by('slug', $slug, 'pa_boja-sociva');
            if ($term) {
                $boja_terms[$slug] = $term->name;
            } else {
                $boja_terms[$slug] = $slug;
            }
        }

        // Get available variations
        $available_variations = $product->get_available_variations();
        $variations_json = wp_json_encode($available_variations);

        // Get cart URL
        $cart_url = wc_get_cart_url();

        // Include the form template
        include(plugin_dir_path(__FILE__) . 'templates/form-template.php');
    }

    /**
     * Handle AJAX add to cart
     */
    public function handle_dual_eye_ajax_add_to_cart() {
        // Debug log
        error_log('AJAX handler called: dual_eye_ajax_add_to_cart');
        error_log('POST data: ' . print_r($_POST, true));

        $product_id = isset($_POST['product_id']) ? absint($_POST['product_id']) : 0;

        if ($product_id != $this->product_id) {
            wp_send_json_error(array('message' => 'Invalid product'));
            return;
        }

        // Get quantities for each eye
        $quantity_levo = isset($_POST['quantity_levo']) ? wc_stock_amount($_POST['quantity_levo']) : 1;
        $quantity_desno = isset($_POST['quantity_desno']) ? wc_stock_amount($_POST['quantity_desno']) : 1;

        // Get form selections
        $sph_levo = isset($_POST['sph_levo']) ? sanitize_text_field($_POST['sph_levo']) : '';
        $boja_levo = isset($_POST['boja_levo']) ? sanitize_text_field($_POST['boja_levo']) : '';
        $sph_desno = isset($_POST['sph_desno']) ? sanitize_text_field($_POST['sph_desno']) : '';
        $boja_desno = isset($_POST['boja_desno']) ? sanitize_text_field($_POST['boja_desno']) : '';

        // Check if we have right eye data
        $has_right_eye = isset($_POST['right_eye']) && $_POST['right_eye'] == 1;
        // If we don't have explicit right eye flag but have right eye data, set it
        if (!$has_right_eye && $sph_desno && $boja_desno) {
            $_POST['right_eye'] = 1;
            error_log('Setting right_eye=1 because we have right eye data');
        }

        // Check if we have left eye data
        $has_left_eye = isset($_POST['left_eye']) && $_POST['left_eye'] == 1;
        // If we don't have explicit left eye flag but have left eye data, set it
        if (!$has_left_eye && $sph_levo && $boja_levo) {
            $_POST['left_eye'] = 1;
            error_log('Setting left_eye=1 because we have left eye data');
        }

        $added_to_cart = false;

        // Log current cart contents before adding
        error_log('Current cart contents before adding:');
        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
            error_log('Item: ' . $cart_item_key . ' - Product: ' . $cart_item['product_id'] . ' - Variation: ' . $cart_item['variation_id'] . ' - Quantity: ' . $cart_item['quantity']);
        }

        // Process left eye
        if ($sph_levo && $boja_levo) {
            error_log('Processing left eye: SPH=' . $sph_levo . ', BOJA=' . $boja_levo . ', QTY=' . $quantity_levo);

            $variation_id = $this->find_variation_id($product_id, array(
                'attribute_pa_lsph' => $sph_levo,
                'attribute_pa_boja-sociva' => $boja_levo
            ));

            error_log('Left eye variation ID: ' . $variation_id);

            if ($variation_id) {
                // Check if this variation with the same eye info is already in cart
                $cart_item_key = $this->find_cart_item_key($product_id, $variation_id, 'levo');

                if ($cart_item_key) {
                    // If already in cart, update quantity
                    error_log('Left eye item found in cart with key: ' . $cart_item_key);
                    $current_qty = WC()->cart->get_cart_item($cart_item_key)['quantity'];
                    $new_qty = $current_qty + $quantity_levo;
                    error_log('Updating left eye quantity from ' . $current_qty . ' to ' . $new_qty);
                    $result = WC()->cart->set_quantity($cart_item_key, $new_qty);
                    if ($result) {
                        $added_to_cart = true;
                        error_log('Left eye quantity updated successfully');
                    } else {
                        error_log('Failed to update left eye quantity');
                    }
                } else {
                    // If not in cart, add new item
                    error_log('Left eye item not found in cart, adding new item with quantity: ' . $quantity_levo);

                    // Set left eye info in cart item data
                    $cart_item_data = array(
                        'eye_info' => 'levo',
                        'unique_key' => 'left_eye_' . $product_id . '_' . $variation_id,
                        'wcff_unique_key' => md5('left_eye_' . $product_id . '_' . $variation_id)
                    );

                    error_log('Cart item data: ' . print_r($cart_item_data, true));

                    // Try to directly set the cart item data
                    WC()->cart->cart_contents_count; // Ensure cart is loaded

                    $result = WC()->cart->add_to_cart(
                        $product_id,
                        $quantity_levo, // Use left eye quantity
                        $variation_id,
                        array(
                            'attribute_pa_lsph' => $sph_levo,
                            'attribute_pa_boja-sociva' => $boja_levo
                        ),
                        $cart_item_data
                    );

                    if ($result) {
                        $added_to_cart = true;
                        error_log('Left eye added to cart successfully with key: ' . $result);
                    } else {
                        error_log('Failed to add left eye to cart');
                    }
                }
            } else {
                error_log('Could not find variation ID for left eye');
            }
        }

        // Process right eye
        if ($sph_desno && $boja_desno) {
            error_log('Processing right eye: SPH=' . $sph_desno . ', BOJA=' . $boja_desno . ', QTY=' . $quantity_desno);

            $variation_id = $this->find_variation_id($product_id, array(
                'attribute_pa_lsph' => $sph_desno,
                'attribute_pa_boja-sociva' => $boja_desno
            ));

            error_log('Right eye variation ID: ' . $variation_id);

            if ($variation_id) {
                // Check if this variation with the same eye info is already in cart
                $cart_item_key = $this->find_cart_item_key($product_id, $variation_id, 'desno');

                if ($cart_item_key) {
                    // If already in cart, update quantity
                    error_log('Right eye item found in cart with key: ' . $cart_item_key);
                    $current_qty = WC()->cart->get_cart_item($cart_item_key)['quantity'];
                    $new_qty = $current_qty + $quantity_desno;
                    error_log('Updating right eye quantity from ' . $current_qty . ' to ' . $new_qty);
                    $result = WC()->cart->set_quantity($cart_item_key, $new_qty);
                    if ($result) {
                        $added_to_cart = true;
                        error_log('Right eye quantity updated successfully');
                    } else {
                        error_log('Failed to update right eye quantity');
                    }
                } else {
                    // If not in cart, add new item
                    error_log('Right eye item not found in cart, adding new item with quantity: ' . $quantity_desno);

                    // Set right eye info in cart item data
                    $cart_item_data = array(
                        'eye_info' => 'desno',
                        'unique_key' => 'right_eye_' . $product_id . '_' . $variation_id,
                        'wcff_unique_key' => md5('right_eye_' . $product_id . '_' . $variation_id)
                    );

                    error_log('Cart item data: ' . print_r($cart_item_data, true));

                    // Try to directly set the cart item data
                    WC()->cart->cart_contents_count; // Ensure cart is loaded

                    $result = WC()->cart->add_to_cart(
                        $product_id,
                        $quantity_desno, // Use right eye quantity
                        $variation_id,
                        array(
                            'attribute_pa_lsph' => $sph_desno,
                            'attribute_pa_boja-sociva' => $boja_desno
                        ),
                        $cart_item_data
                    );

                    if ($result) {
                        $added_to_cart = true;
                        error_log('Right eye added to cart successfully with key: ' . $result);
                    } else {
                        error_log('Failed to add right eye to cart');
                    }
                }
            } else {
                error_log('Could not find variation ID for right eye');
            }
        }

        // Log cart contents after adding
        error_log('Cart contents after adding:');
        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
            error_log('Item: ' . $cart_item_key . ' - Product: ' . $cart_item['product_id'] . ' - Variation: ' . $cart_item['variation_id'] . ' - Quantity: ' . $cart_item['quantity'] . ' - Eye Info: ' . (isset($cart_item['eye_info']) ? $cart_item['eye_info'] : 'not set'));
        }

        if ($added_to_cart) {
            WC_AJAX::get_refreshed_fragments();
        } else {
            wp_send_json_error(array('message' => 'Failed to add to cart'));
        }

        wp_die();
    }

    /**
     * Helper function to find variation ID
     */
    private function find_variation_id($product_id, $attributes) {
        $product = wc_get_product($product_id);
        if (!$product || $product->get_type() !== 'variable') {
            return 0;
        }

        $data_store = WC_Data_Store::load('product');
        return $data_store->find_matching_product_variation($product, $attributes);
    }

    /**
     * Helper function to find cart item key by eye info
     */
    private function find_cart_item_key($product_id, $variation_id, $eye_info) {
        // Debug log
        error_log('Looking for cart item: Product ID=' . $product_id . ', Variation ID=' . $variation_id . ', Eye Info=' . $eye_info);

        // Generate the expected cart ID
        $data_store = WC_Data_Store::load('product');
        $product = wc_get_product($product_id);

        // Base cart ID that WooCommerce would generate
        $base_cart_id = $data_store->get_cart_id($product_id, $variation_id, array(), array(
            'attribute_pa_lsph' => $product->get_variation_attributes()['pa_lsph'],
            'attribute_pa_boja-sociva' => $product->get_variation_attributes()['pa_boja-sociva']
        ));

        // Expected cart ID with eye info
        $expected_cart_id = $base_cart_id . '_' . $eye_info;
        error_log('Expected cart ID: ' . $expected_cart_id);

        // First try to find by exact cart ID
        if (isset(WC()->cart->cart_contents[$expected_cart_id])) {
            error_log('Found exact matching cart item with key: ' . $expected_cart_id);
            return $expected_cart_id;
        }

        // If not found by exact ID, search through all cart items
        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
            error_log('Cart item: Key=' . $cart_item_key . ', Product ID=' . $cart_item['product_id'] . ', Variation ID=' . $cart_item['variation_id'] . ', Eye Info=' . (isset($cart_item['eye_info']) ? $cart_item['eye_info'] : 'not set'));

            if ($cart_item['product_id'] == $product_id &&
                $cart_item['variation_id'] == $variation_id &&
                isset($cart_item['eye_info']) &&
                $cart_item['eye_info'] == $eye_info) {
                error_log('Found matching cart item with key: ' . $cart_item_key);
                return $cart_item_key;
            }
        }
        error_log('No matching cart item found');
        return false;
    }

    /**
     * Display eye info in cart
     */
    public function display_eye_info_in_cart($item_data, $cart_item) {
        error_log('Display eye info for cart item: ' . print_r($cart_item, true));

        // Only process our specific product
        if ($cart_item['product_id'] != $this->product_id) {
            return $item_data;
        }

        // Remove any existing SPH and BOJA entries that might be added by WooCommerce
        foreach ($item_data as $key => $data) {
            if ($data['key'] == 'SPH' || $data['key'] == 'BOJA' ||
                $data['key'] == 'pa_lsph' || $data['key'] == 'pa_boja-sociva') {
                unset($item_data[$key]);
            }
        }

        // Reindex the array after removing items
        $item_data = array_values($item_data);

        // Get product variation
        $product_variation = wc_get_product($cart_item['variation_id']);

        if ($product_variation) {
            // Get variation attributes
            $attributes = $product_variation->get_attributes();

            // Get SPH and BOJA values
            $sph_slug = isset($attributes['pa_lsph']) ? $attributes['pa_lsph'] : '';
            $boja_slug = isset($attributes['pa_boja-sociva']) ? $attributes['pa_boja-sociva'] : '';

            // Get proper attribute names
            $sph_term = get_term_by('slug', $sph_slug, 'pa_lsph');
            $boja_term = get_term_by('slug', $boja_slug, 'pa_boja-sociva');

            $sph_name = $sph_term ? $sph_term->name : $sph_slug;
            $boja_name = $boja_term ? $boja_term->name : $boja_slug;

            // Add SPH and BOJA to cart item data
            if ($sph_name) {
                $item_data[] = array(
                    'key'   => 'SPH',
                    'value' => $sph_name
                );
            }

            if ($boja_name) {
                $item_data[] = array(
                    'key'   => 'BOJA',
                    'value' => $boja_name
                );
            }
        }

        if (isset($cart_item['eye_info'])) {
            $eye = $cart_item['eye_info'];
            $quantity = $cart_item['quantity'];
            error_log('Eye info found: ' . $eye . ', Quantity: ' . $quantity);

            $item_data[] = array(
                'key'   => 'Oko',
                'value' => $eye == 'levo' ? 'Levo oko x ' . $quantity : 'Desno oko x ' . $quantity
            );
        } else {
            error_log('No eye info found in cart item');
        }

        return $item_data;
    }

    /**
     * Save eye info to order item
     */
    public function add_eye_info_to_order_items($item, $cart_item_key, $values, $order) {
        error_log('Adding eye info to order item: ' . print_r($values, true));

        // Only process our specific product
        if ($values['product_id'] != $this->product_id) {
            return;
        }

        // Remove any existing attribute meta that might be added by WooCommerce
        $meta_data = $item->get_meta_data();
        foreach ($meta_data as $meta) {
            if ($meta->key == 'pa_lsph' || $meta->key == 'pa_boja-sociva') {
                $item->delete_meta_data($meta->key);
            }
        }

        // Get product variation
        $product_variation = wc_get_product($values['variation_id']);

        if ($product_variation) {
            // Get variation attributes
            $attributes = $product_variation->get_attributes();

            // Get SPH and BOJA values
            $sph_slug = isset($attributes['pa_lsph']) ? $attributes['pa_lsph'] : '';
            $boja_slug = isset($attributes['pa_boja-sociva']) ? $attributes['pa_boja-sociva'] : '';

            // Get proper attribute names
            $sph_term = get_term_by('slug', $sph_slug, 'pa_lsph');
            $boja_term = get_term_by('slug', $boja_slug, 'pa_boja-sociva');

            $sph_name = $sph_term ? $sph_term->name : $sph_slug;
            $boja_name = $boja_term ? $boja_term->name : $boja_slug;

            // Add SPH and BOJA to order item meta
            if ($sph_name) {
                $item->add_meta_data('SPH', $sph_name);
            }

            if ($boja_name) {
                $item->add_meta_data('BOJA', $boja_name);
            }
        }

        if (isset($values['eye_info'])) {
            $eye = $values['eye_info'];
            $quantity = $values['quantity'];
            error_log('Eye info found in order: ' . $eye . ', Quantity: ' . $quantity);
            $item->add_meta_data('Oko', $eye == 'levo' ? 'Levo oko x ' . $quantity : 'Desno oko x ' . $quantity);
        } else {
            error_log('No eye info found in order item');
        }
    }

    /**
     * Filter to ensure eye_info is stored in cart item data
     */
    public function add_eye_info_to_cart_item_data($cart_item_data, $product_id, $variation_id) {
        error_log('Adding cart item data filter called');
        error_log('POST data in add_eye_info_to_cart_item_data: ' . print_r($_POST, true));

        // Get form selections
        $sph_levo = isset($_POST['sph_levo']) ? sanitize_text_field($_POST['sph_levo']) : '';
        $boja_levo = isset($_POST['boja_levo']) ? sanitize_text_field($_POST['boja_levo']) : '';
        $sph_desno = isset($_POST['sph_desno']) ? sanitize_text_field($_POST['sph_desno']) : '';
        $boja_desno = isset($_POST['boja_desno']) ? sanitize_text_field($_POST['boja_desno']) : '';

        // Check if this is for the right eye based on the form data
        $is_right_eye = false;
        $is_left_eye = false;

        // Check if the current variation matches the left eye attributes
        if ($sph_levo && $boja_levo) {
            $left_variation_id = $this->find_variation_id($product_id, array(
                'attribute_pa_lsph' => $sph_levo,
                'attribute_pa_boja-sociva' => $boja_levo
            ));

            if ($left_variation_id == $variation_id) {
                $is_left_eye = true;
                error_log('Identified as left eye based on attributes');
            }
        }

        // Check if the current variation matches the right eye attributes
        if ($sph_desno && $boja_desno) {
            $right_variation_id = $this->find_variation_id($product_id, array(
                'attribute_pa_lsph' => $sph_desno,
                'attribute_pa_boja-sociva' => $boja_desno
            ));

            if ($right_variation_id == $variation_id) {
                $is_right_eye = true;
                error_log('Identified as right eye based on attributes');
            }
        }

        // Check explicit flags
        if (isset($_POST['left_eye']) && $_POST['left_eye'] == 1) {
            $is_left_eye = true;
            error_log('Identified as left eye based on left_eye flag');
        }

        if (isset($_POST['right_eye']) && $_POST['right_eye'] == 1) {
            $is_right_eye = true;
            error_log('Identified as right eye based on right_eye flag');
        }

        // If both flags are set, use the variation ID to determine which eye
        if ($is_left_eye && $is_right_eye) {
            if ($variation_id == $left_variation_id) {
                $is_right_eye = false;
                error_log('Both eyes detected, but variation matches left eye');
            } else if ($variation_id == $right_variation_id) {
                $is_left_eye = false;
                error_log('Both eyes detected, but variation matches right eye');
            }
        }

        if ($is_right_eye && !$is_left_eye) {
            error_log('Setting right eye info in cart item data');
            $cart_item_data['eye_info'] = 'desno';
            // Add a unique key for right eye
            $cart_item_data['unique_key'] = 'right_eye_' . $product_id . '_' . $variation_id;
            $cart_item_data['wcff_unique_key'] = md5('right_eye_' . $product_id . '_' . $variation_id);
        } else {
            error_log('Setting left eye info in cart item data');
            $cart_item_data['eye_info'] = 'levo';
            // Add a unique key for left eye
            $cart_item_data['unique_key'] = 'left_eye_' . $product_id . '_' . $variation_id;
            $cart_item_data['wcff_unique_key'] = md5('left_eye_' . $product_id . '_' . $variation_id);
        }

        error_log('Cart item data after filter: ' . print_r($cart_item_data, true));
        return $cart_item_data;
    }

    /**
     * Hide default variation attributes in cart
     */
    public function hide_variation_attributes_in_cart($visible, $cart_item, $cart_item_key) {
        return $visible;
    }

    /**
     * Hide variation attributes in product name
     */
    public function hide_attributes_in_product_name($is_in_name, $attribute, $name) {
        global $woocommerce;

        // Check if we're in the cart or checkout
        if (is_cart() || is_checkout()) {
            // Get the current cart
            $cart = WC()->cart->get_cart();

            // Check if any cart item is our specific product
            foreach ($cart as $cart_item) {
                if ($cart_item['product_id'] == $this->product_id) {
                    // Hide attributes in product name
                    return false;
                }
            }
        }

        return $is_in_name;
    }

    /**
     * Filter to generate a unique cart item key based on eye info
     */
    public function unique_cart_id_for_eye_info($cart_id, $product_id, $variation_id, $cart_item_data) {
        error_log('Cart ID filter called for ID: ' . $cart_id);

        if (isset($cart_item_data['eye_info'])) {
            $eye_info = $cart_item_data['eye_info'];
            $new_id = $cart_id . '_' . $eye_info;
            error_log('Modified cart ID from ' . $cart_id . ' to ' . $new_id);
            return $new_id;
        }

        return $cart_id;
    }
}

// Initialize the plugin
function dual_eye_options_airoptix_init() {
    // Check if WooCommerce is active
    if (!class_exists('WooCommerce')) {
        add_action('admin_notices', 'dual_eye_options_airoptix_woocommerce_notice');
        return;
    }

    // Create an instance of the plugin class
    $dual_eye_options = new Dual_Eye_Options_AirOptix();
}

// Display admin notice if WooCommerce is not active
function dual_eye_options_airoptix_woocommerce_notice() {
    ?>
    <div class="error">
        <p><?php _e('Dual Eye Options for AirOptix requires WooCommerce to be installed and activated.', 'dual-eye-options'); ?></p>
    </div>
    <?php
}

// Hook into WordPress init
add_action('init', 'dual_eye_options_airoptix_init');