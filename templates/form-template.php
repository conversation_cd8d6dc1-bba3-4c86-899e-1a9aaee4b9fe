<form class="cart" method="post" enctype="multipart/form-data">
    <div class="woocommerce dual-eye-options-container">
        <div class="dual-options-form-wrapper">
            <div class="eye-options">
                <h3 style="font-family: 'Outfit', sans-serif;">Levo oko</h3>
                <div class="option-row">
                    <label for="sph_levo">SPH</label>
                    <select id="sph_levo" name="sph_levo">
                        <option value="">Odaberite opciju</option>
                        <?php foreach ($sph_options as $sph) : ?>
                            <option value="<?php echo esc_attr($sph); ?>"><?php echo esc_html($sph_terms[$sph]); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="option-row">
                    <label for="boja_levo">BOJA</label>
                    <select id="boja_levo" name="boja_levo">
                        <option value="">Odaberite opciju</option>
                        <?php foreach ($boja_options as $boja) : ?>
                            <option value="<?php echo esc_attr($boja); ?>"><?php echo esc_html($boja_terms[$boja]); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="option-row quantity-row">
                    <label for="quantity_levo">KOLIČINA</label>
                    <div class="quantity-controls">
                        <button type="button" class="quantity-btn minus" data-target="quantity_levo">-</button>
                        <div class="qty-input-wrapper">
                            <input type="number" id="quantity_levo" class="input-text qty text" step="1" min="1" max="" name="quantity_levo" value="1" title="Qty" size="4" inputmode="numeric">
                        </div>
                        <button type="button" class="quantity-btn plus" data-target="quantity_levo">+</button>
                    </div>
                </div>
            </div>

            <div class="vertical-divider"></div>

            <div class="eye-options">
                <h3 style="font-family: 'Outfit', sans-serif;">Desno oko</h3>
                <div class="option-row">
                    <label for="sph_desno">SPH</label>
                    <select id="sph_desno" name="sph_desno">
                        <option value="">Odaberite opciju</option>
                        <?php foreach ($sph_options as $sph) : ?>
                            <option value="<?php echo esc_attr($sph); ?>"><?php echo esc_html($sph_terms[$sph]); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="option-row">
                    <label for="boja_desno">BOJA</label>
                    <select id="boja_desno" name="boja_desno">
                        <option value="">Odaberite opciju</option>
                        <?php foreach ($boja_options as $boja) : ?>
                            <option value="<?php echo esc_attr($boja); ?>"><?php echo esc_html($boja_terms[$boja]); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="option-row quantity-row">
                    <label for="quantity_desno">KOLIČINA</label>
                    <div class="quantity-controls">
                        <button type="button" class="quantity-btn minus" data-target="quantity_desno">-</button>
                        <div class="qty-input-wrapper">
                            <input type="number" id="quantity_desno" class="input-text qty text" step="1" min="1" max="" name="quantity_desno" value="1" title="Qty" size="4" inputmode="numeric">
                        </div>
                        <button type="button" class="quantity-btn plus" data-target="quantity_desno">+</button>
                    </div>
                </div>
            </div>

            <div class="add-to-cart-row">
                <input type="hidden" name="product_id" value="<?php echo esc_attr($product->get_id()); ?>">
                <input type="hidden" name="dual_eye_form" value="1">
                <div class="button-group">
                    <button type="button" id="reset-selections" class="reset-button">Očisti</button>
                    <button type="submit" name="add-to-cart" value="<?php echo esc_attr($product->get_id()); ?>" class="single_add_to_cart_button button alt">DODAJ U KORPU</button>
                </div>
            </div>
            <div class="form-message"></div>
        </div>
    </div>
</form>

<style>
    /* Custom styling */
    .dual-eye-options-container {
        margin-bottom: 30px;
    }
    .dual-options-form-wrapper {
        display: flex;
        flex-wrap: wrap;
        justify-content:space-between;
        position: relative;
    }
    .eye-options {
        width: 45%;
        margin-bottom: 20px;
        position: relative;
    }
    .vertical-divider {
        width: 1px;
        background-color: #ddd;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        top: 0;
        bottom:150;
    }

    .eye-options h3 {
        color:#F38120;
        text-transform: uppercase;
    }
    .option-row {
        margin-bottom: 15px;
    }
    .option-row label {
        display: block;
        font-family: 'Outfit', sans-serif;
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 5px;
    }
    .option-row select {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-family: 'Outfit', sans-serif;
        font-size: 16px;
    }
    .add-to-cart-row {
        width: 100%;
        display: flex;
        align-items: center;
        margin-top: 15px;
    }
    .qty-input-wrapper {
        display: inline-block;
    }
    .quantity-row input {
        width: 40px;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-family: 'Outfit', sans-serif;
        font-size: 16px;
        text-align: center;
        margin: 0 20px;
        height: 40px;
        box-sizing: border-box;
        /* Remove spinner arrows for all browsers */
        -moz-appearance: textfield; /* Firefox */
    }
    /* Remove spinner arrows for Chrome, Safari, Edge, Opera */
    .quantity-row input::-webkit-outer-spin-button,
    .quantity-row input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }
    .quantity-controls {
        display: flex;
        align-items: center;
        justify-content: flex-start;
    }
    .quantity-btn {
        width: 40px;
        height: 40px;
        background-color: white!important;
        color: #333;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 18px;
        line-height: 1;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        margin:0 !important;
        box-sizing: border-box;
    }
    .quantity-btn:hover {
        color:white;
    }
    .elementor-379 .elementor-element.elementor-element-4b0dc44 button:hover {
        background-color: #F38120!important;
    }
    
    .single_add_to_cart_button {
        background-color: #f5c6a1 !important;
        color: white !important;
        font-family: 'Outfit', sans-serif;
        font-size: 16px;
        padding: 12px 25px;
        margin: 0 !important;
        text-transform: uppercase;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .single_add_to_cart_button:hover {
        background-color: #f0b78e !important;
    }
    .button-group {
        display: flex;
        flex-direction: column;
        gap: 10px;
        width: 100%;
    }
    .reset-button {
        background-color: white !important;
        background-image: none !important;
        color: #333 !important;
        font-family: 'Outfit', sans-serif !important;
        font-size: 14px !important;
        padding: 0 !important;
        margin: 0 0 5px 0 !important;
        border: none !important;
        box-shadow: none !important;
        cursor: pointer;
        text-align: center;
        text-decoration: none !important;
        width: 100%;
        min-height: auto !important;
        line-height: normal !important;
        transition: none !important;
    }
    .form-message {
        margin-top: 10px;
        width: 100%;
        padding: 10px;
        font-family: 'Outfit', sans-serif;
    }
    .form-message.error {
        color: red;
        background-color: #fff0f0;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 10px;
    }
    button#reset-selections {
        background-color: transparent !important;
    }

    /* Mobile responsive styles */
    @media (max-width: 768px) {
        .eye-options {
            width: 100%;
            margin-right: 0;
        }
        .eye-options:first-child:after {
            display: none;
        }
        .vertical-divider {
            display: none;
        }
        .option-row label,
        .option-row select {
            font-size: 14px;
        }
        .single_add_to_cart_button {
            width: 100%;
            padding: 10px 15px;
        }
        .button-group {
            width: 100%;
        }
        .quantity-controls {
            justify-content: center;
        }
        .quantity-btn {
            width: 25px;
            height: 30px;
            font-size: 16px;
            margin: 0 5px;
            box-sizing: border-box;
        }
        .quantity-row input {
            height: 30px;
            box-sizing: border-box;
        }
    }
</style>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Store variations data
    var variations = <?php echo $variations_json; ?>;
    
    // Form submission is handled by the script registered in enqueue_custom_cart_script
    
    // Reset button functionality
    $('#reset-selections').on('click', function() {
        // Reset all select elements to their first option
        $('#sph_levo, #boja_levo, #sph_desno, #boja_desno').each(function() {
            $(this).val('').trigger('change');
        });
        
        // Reset quantities to 1
        $('#quantity_levo, #quantity_desno').val(1);
        
        // Clear any form messages
        $('.form-message').empty();
        
        // Show a message that selections have been reset
        $('.form-message').html('<div style="color: green; padding: 10px; background-color: #f0fff0; border: 1px solid #ddd; border-radius: 4px;">Odabiri i količine su poništeni.</div>');
        
        // Hide the message after 3 seconds
        setTimeout(function() {
            $('.form-message').empty();
        }, 3000);
    });
    
    // Quantity plus button functionality
    $('.quantity-btn.plus').on('click', function() {
        var targetInput = $(this).data('target');
        var $input = $('#' + targetInput);
        var currentVal = parseInt($input.val());
        
        if (!isNaN(currentVal)) {
            $input.val(currentVal + 1);
        } else {
            $input.val(1);
        }
    });
    
    // Quantity minus button functionality
    $('.quantity-btn.minus').on('click', function() {
        var targetInput = $(this).data('target');
        var $input = $('#' + targetInput);
        var currentVal = parseInt($input.val());
        
        if (!isNaN(currentVal) && currentVal > 1) {
            $input.val(currentVal - 1);
        } else {
            $input.val(1);
        }
    });
});
</script>
