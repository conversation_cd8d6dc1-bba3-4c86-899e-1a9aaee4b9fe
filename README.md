# Dual Eye Options for AirOptix

A WordPress plugin that provides a custom dual eye options selection form for AirOptix contact lenses.

## Description

This plugin creates a custom form for selecting different options for left and right eyes when purchasing AirOptix contact lenses. It replaces the default WooCommerce variation form with a specialized interface that allows customers to:

- Select different SPH values for each eye
- Select different colors for each eye
- Set different quantities for each eye
- Add both eyes to cart in a single action

## Installation

1. Upload the `dual-eye-options-airoptix` folder to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. The plugin will automatically apply to the product with ID 222486 (AirOptix)

## Requirements

- WordPress 5.0 or higher
- WooCommerce 3.0 or higher
- PHP 7.2 or higher

## Configuration

The plugin is pre-configured to work with AirOptix contact lenses (product ID: 222486). If you need to use it with a different product, you can modify the product ID in the plugin code.

To change the product ID:

1. Open the `dual-eye-options-airoptix.php` file
2. Find the line: `private $product_id = 222486;`
3. Change the number to your product ID
4. Save the file

## Features

- Custom form for selecting different options for left and right eyes
- Validation to ensure complete selections (SPH and color for each eye)
- Quantity controls for each eye
- Reset button to clear selections
- AJAX-based cart addition without page reload
- Automatic form reset after successful cart addition
- Form remains active after adding to cart, allowing for continuous selections
- Success message with automatic clearing after 3 seconds
- Proper display of eye information in cart and checkout
- Mobile-responsive design

## Customization

The plugin includes CSS styling that can be customized to match your theme. The styles are included in the `templates/form-template.php` file.

## Support

For support or feature requests, please contact the plugin author.

## License

This plugin is licensed under the GPL v2 or later.

## Changelog

### Version 1.0.1
- Fixed issue with "Add to Cart" button remaining disabled after successful cart addition
- Added automatic form reset functionality
- Improved user experience with temporary success messages
- Enhanced form to allow continuous product selections

### Version 1.0.0
- Initial release

## Credits

Developed based on custom code for AirOptix contact lenses.
